﻿using Microsoft.EntityFrameworkCore;
using TaskTracker.Core.Entities;

namespace TaskTracker.Data
{
    public class TaskTrackerDbContext : DbContext
    {
        public TaskTrackerDbContext(DbContextOptions<TaskTrackerDbContext> options) : base(options)
        {
        }

        public DbSet<TaskItem> Tasks { get; set; }
        public DbSet<Email> Emails { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure TaskItem entity
            modelBuilder.Entity<TaskItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");

                // Configure relationship with Email
                entity.HasOne(t => t.Email)
                      .WithMany(e => e.Tasks)
                      .HasForeignKey(t => t.EmailId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Email entity
            modelBuilder.Entity<Email>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Subject).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Body).IsRequired();
                entity.Property(e => e.FromAddress).IsRequired().HasMaxLength(255);
                entity.Property(e => e.ProcessingStatus).HasConversion<int>();
                entity.Property(e => e.ProcessedAt).HasDefaultValueSql("GETUTCDATE()");

                // Create index on MessageId for faster lookups
                entity.HasIndex(e => e.MessageId).IsUnique();
            });
        }
    }
}

using TaskTracker.Core.Entities;

namespace TaskTracker.Core.Interfaces
{
    public interface IEmailService
    {
        System.Threading.Tasks.Task<IEnumerable<Email>> FetchNewEmailsAsync();
        System.Threading.Tasks.Task ProcessEmailAsync(Email email);
        System.Threading.Tasks.Task<IEnumerable<TaskItem>> ExtractTasksFromEmailAsync(Email email);
        System.Threading.Tasks.Task MarkEmailAsProcessedAsync(int emailId);
        System.Threading.Tasks.Task<bool> TestEmailConnectionAsync();
    }
}

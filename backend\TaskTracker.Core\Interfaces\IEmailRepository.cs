using TaskTracker.Core.Entities;

namespace TaskTracker.Core.Interfaces
{
    public interface IEmailRepository
    {
        System.Threading.Tasks.Task<IEnumerable<Email>> GetAllAsync();
        System.Threading.Tasks.Task<Email?> GetByIdAsync(int id);
        System.Threading.Tasks.Task<IEnumerable<Email>> GetByStatusAsync(EmailProcessingStatus status);
        System.Threading.Tasks.Task<IEnumerable<Email>> GetUnprocessedEmailsAsync();
        System.Threading.Tasks.Task<Email> CreateAsync(Email email);
        System.Threading.Tasks.Task<Email> UpdateAsync(Email email);
        System.Threading.Tasks.Task<bool> DeleteAsync(int id);
        System.Threading.Tasks.Task<Email?> GetByMessageIdAsync(string messageId);
        System.Threading.Tasks.Task<IEnumerable<Email>> GetEmailsWithTasksAsync();
        System.Threading.Tasks.Task<int> GetEmailCountByStatusAsync(EmailProcessingStatus status);
    }
}

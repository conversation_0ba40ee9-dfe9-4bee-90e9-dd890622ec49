using TaskTracker.Core.Entities;

namespace TaskTracker.Core.Interfaces
{
    public interface ITaskRepository
    {
        System.Threading.Tasks.Task<IEnumerable<TaskItem>> GetAllAsync();
        System.Threading.Tasks.Task<TaskItem?> GetByIdAsync(int id);
        System.Threading.Tasks.Task<IEnumerable<TaskItem>> GetByStatusAsync(Entities.TaskStatus status);
        System.Threading.Tasks.Task<IEnumerable<TaskItem>> GetByPriorityAsync(TaskPriority priority);
        System.Threading.Tasks.Task<IEnumerable<TaskItem>> GetByEmailIdAsync(int emailId);
        System.Threading.Tasks.Task<IEnumerable<TaskItem>> GetTasksDueSoonAsync(DateTime beforeDate);
        System.Threading.Tasks.Task<TaskItem> CreateAsync(TaskItem task);
        System.Threading.Tasks.Task<TaskItem> UpdateAsync(TaskItem task);
        System.Threading.Tasks.Task<bool> DeleteAsync(int id);
        System.Threading.Tasks.Task<IEnumerable<TaskItem>> SearchAsync(string searchTerm);
        System.Threading.Tasks.Task<int> GetTaskCountByStatusAsync(Entities.TaskStatus status);
    }
}

﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TaskTracker.API", "TaskTracker.API\TaskTracker.API.csproj", "{910723C0-DAAA-4B70-B90E-17139B5BB30B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TaskTracker.Core", "TaskTracker.Core\TaskTracker.Core.csproj", "{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TaskTracker.Data", "TaskTracker.Data\TaskTracker.Data.csproj", "{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TaskTracker.Services", "TaskTracker.Services\TaskTracker.Services.csproj", "{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Debug|x64.Build.0 = Debug|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Debug|x86.Build.0 = Debug|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Release|Any CPU.Build.0 = Release|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Release|x64.ActiveCfg = Release|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Release|x64.Build.0 = Release|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Release|x86.ActiveCfg = Release|Any CPU
		{910723C0-DAAA-4B70-B90E-17139B5BB30B}.Release|x86.Build.0 = Release|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Debug|x64.Build.0 = Debug|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Debug|x86.Build.0 = Debug|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Release|Any CPU.Build.0 = Release|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Release|x64.ActiveCfg = Release|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Release|x64.Build.0 = Release|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Release|x86.ActiveCfg = Release|Any CPU
		{71FE2798-E1C5-4E20-976C-D6A4BD77AFF9}.Release|x86.Build.0 = Release|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Debug|x64.Build.0 = Debug|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Debug|x86.Build.0 = Debug|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Release|x64.ActiveCfg = Release|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Release|x64.Build.0 = Release|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Release|x86.ActiveCfg = Release|Any CPU
		{97D2AB80-3BDB-482A-B213-4BF9F692A7C3}.Release|x86.Build.0 = Release|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Debug|x64.Build.0 = Debug|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Debug|x86.Build.0 = Debug|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Release|Any CPU.Build.0 = Release|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Release|x64.ActiveCfg = Release|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Release|x64.Build.0 = Release|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Release|x86.ActiveCfg = Release|Any CPU
		{7DE8AB1F-0AD0-4C9F-82A5-53204B979B7E}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal

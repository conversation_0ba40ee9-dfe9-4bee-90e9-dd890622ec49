using TaskTracker.Core.Entities;

namespace TaskTracker.Core.Interfaces
{
    public interface IAiService
    {
        System.Threading.Tasks.Task<IEnumerable<TaskItem>> ExtractTasksFromTextAsync(string text, string context = "");
        System.Threading.Tasks.Task<TaskPriority> DeterminePriorityAsync(string taskText);
        System.Threading.Tasks.Task<string?> CategorizeTaskAsync(string taskText);
        System.Threading.Tasks.Task<DateTime?> ExtractDueDateAsync(string taskText);
        System.Threading.Tasks.Task<double> CalculateConfidenceScoreAsync(string originalText, TaskItem extractedTask);
        System.Threading.Tasks.Task<string> SummarizeEmailAsync(string emailBody);
    }
}

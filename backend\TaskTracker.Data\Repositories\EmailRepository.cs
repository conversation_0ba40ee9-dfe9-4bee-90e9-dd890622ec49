using Microsoft.EntityFrameworkCore;
using TaskTracker.Core.Entities;
using TaskTracker.Core.Interfaces;

namespace TaskTracker.Data.Repositories
{
    public class EmailRepository : IEmailRepository
    {
        private readonly TaskTrackerDbContext _context;

        public EmailRepository(TaskTrackerDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Email>> GetAllAsync()
        {
            return await _context.Emails
                .Include(e => e.Tasks)
                .OrderByDescending(e => e.ReceivedAt)
                .ToListAsync();
        }

        public async Task<Email?> GetByIdAsync(int id)
        {
            return await _context.Emails
                .Include(e => e.Tasks)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<IEnumerable<Email>> GetByStatusAsync(EmailProcessingStatus status)
        {
            return await _context.Emails
                .Include(e => e.Tasks)
                .Where(e => e.ProcessingStatus == status)
                .OrderByDescending(e => e.ReceivedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Email>> GetUnprocessedEmailsAsync()
        {
            return await _context.Emails
                .Where(e => e.ProcessingStatus == EmailProcessingStatus.Pending)
                .OrderBy(e => e.ReceivedAt)
                .ToListAsync();
        }

        public async Task<Email> CreateAsync(Email email)
        {
            _context.Emails.Add(email);
            await _context.SaveChangesAsync();
            return email;
        }

        public async Task<Email> UpdateAsync(Email email)
        {
            _context.Emails.Update(email);
            await _context.SaveChangesAsync();
            return email;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var email = await _context.Emails.FindAsync(id);
            if (email == null) return false;

            _context.Emails.Remove(email);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<Email?> GetByMessageIdAsync(string messageId)
        {
            return await _context.Emails
                .Include(e => e.Tasks)
                .FirstOrDefaultAsync(e => e.MessageId == messageId);
        }

        public async Task<IEnumerable<Email>> GetEmailsWithTasksAsync()
        {
            return await _context.Emails
                .Include(e => e.Tasks)
                .Where(e => e.Tasks.Any())
                .OrderByDescending(e => e.ReceivedAt)
                .ToListAsync();
        }

        public async Task<int> GetEmailCountByStatusAsync(EmailProcessingStatus status)
        {
            return await _context.Emails.CountAsync(e => e.ProcessingStatus == status);
        }
    }
}

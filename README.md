# Task Tracking System

An intelligent task tracking system that automatically extracts and tracks tasks from emails using AI/LLM technology.

## Architecture

- **Backend**: .NET 8 Web API with Entity Framework Core
- **Frontend**: React with Ant Design (antd)
- **Database**: Microsoft SQL Server
- **AI Integration**: LLM for email task extraction
- **Email Processing**: Automated email reading and task classification

## Project Structure

```
Task/
├── backend/                 # .NET Web API
│   ├── TaskTracker.API/     # Main API project
│   ├── TaskTracker.Core/    # Domain models and interfaces
│   ├── TaskTracker.Data/    # Data access layer
│   └── TaskTracker.Services/ # Business logic and services
├── frontend/                # React application
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── public/
└── docs/                   # Documentation
```

## Features

### Core Features
- ✅ Email integration and monitoring
- ✅ AI-powered task extraction from emails
- ✅ Task status tracking and management
- ✅ User dashboard with task overview
- ✅ Real-time notifications
- ✅ Task categorization and prioritization

### Technical Features
- RESTful API design
- Entity Framework Code First approach
- JWT authentication
- Real-time updates with SignalR
- Responsive UI with Ant Design
- Email service integration (IMAP/POP3)
- LLM integration for natural language processing

## Getting Started

### Prerequisites
- .NET 8 SDK
- Node.js 18+ and npm/yarn
- SQL Server (LocalDB or full instance)
- Email account for integration

### Backend Setup
```bash
cd backend
dotnet restore
dotnet ef database update
dotnet run --project TaskTracker.API
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## Development Roadmap

1. **Phase 1**: Project setup and basic structure
2. **Phase 2**: Backend API development
3. **Phase 3**: Database design and implementation
4. **Phase 4**: Email processing service
5. **Phase 5**: Frontend development
6. **Phase 6**: Integration and testing

## Contributing

Please read the development guidelines in `/docs/DEVELOPMENT.md` before contributing.

## License

This project is licensed under the MIT License.

using System.ComponentModel.DataAnnotations;

namespace TaskTracker.Core.Entities
{
    public class Email
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(500)]
        public string Subject { get; set; } = string.Empty;
        
        [Required]
        public string Body { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(255)]
        public string FromAddress { get; set; } = string.Empty;
        
        public string? ToAddresses { get; set; }
        
        public string? CcAddresses { get; set; }
        
        public DateTime ReceivedAt { get; set; }
        
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
        
        public EmailProcessingStatus ProcessingStatus { get; set; } = EmailProcessingStatus.Pending;
        
        public string? ProcessingError { get; set; }
        
        // Email metadata
        public string? MessageId { get; set; }
        
        public string? ThreadId { get; set; }
        
        public bool IsRead { get; set; } = false;
        
        public bool HasAttachments { get; set; } = false;
        
        // AI processing results
        public string? ExtractedTasks { get; set; } // JSON array of extracted tasks
        
        public double? ProcessingConfidence { get; set; }
        
        public string? AiProcessingNotes { get; set; }
        
        // Navigation properties
        public ICollection<TaskItem> Tasks { get; set; } = new List<TaskItem>();
    }
    
    public enum EmailProcessingStatus
    {
        Pending = 0,
        Processing = 1,
        Completed = 2,
        Failed = 3,
        Skipped = 4
    }
}

# .NET
bin/
obj/
*.user
*.suo
*.cache
*.docstates
*.dll
*.pdb
*.exe
*.log
*.tmp
*.temp
*.swp
*.swo
*~
.vs/
.vscode/
*.userprefs
*.pidb
*.booproj
*.svd
*.pch
*.tlog
*.log
*.scc
*.exp
*.ilk
*.lastbuildstate
*.lib
*.sbr
*.sdf
*.opensdf
*.unsuccessfulbuild
ipch/
[Oo]bj/
[Bb]in/
[Dd]ebug*/
[Rr]elease*/
Ankh.NoLoad

# Node.js / React
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache
.env.local
.env.development.local
.env.test.local
.env.production.local
build/
dist/
coverage/

# Database
*.mdf
*.ldf
*.sdf
*.db
*.sqlite
*.sqlite3

# Environment files
.env
.env.local
.env.development
.env.production
appsettings.Development.json
appsettings.Production.json

# IDE
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

﻿using System.ComponentModel.DataAnnotations;

namespace TaskTracker.Core.Entities
{
    public class TaskItem
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(500)]
        public string Title { get; set; } = string.Empty;

        public string? Description { get; set; }

        public TaskStatus Status { get; set; } = TaskStatus.Pending;

        public TaskPriority Priority { get; set; } = TaskPriority.Medium;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        public DateTime? DueDate { get; set; }

        public string? AssignedTo { get; set; }

        public string? Category { get; set; }

        public string? Tags { get; set; }

        // Email related properties
        public int? EmailId { get; set; }
        public Email? Email { get; set; }

        // AI extraction metadata
        public double? ConfidenceScore { get; set; }
        public string? ExtractedContext { get; set; }
    }

    public enum TaskStatus
    {
        Pending = 0,
        InProgress = 1,
        Completed = 2,
        Cancelled = 3,
        OnHold = 4
    }

    public enum TaskPriority
    {
        Low = 0,
        Medium = 1,
        High = 2,
        Critical = 3
    }
}
